import 'package:asr_plugin/asr_plugin.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/system_secret_model/system_secret_model.dart';

class AsrUtil {
  static ASRControllerConfig? _config;

  static Future<ASRControllerConfig> config() async {
    if (_config != null) {
      return Future.value(_config);
    }
    SystemSecretModel model = await Api.getASRSystemSecret();
    ASRControllerConfig config = ASRControllerConfig()
      ..is_save_audio_file = true
      ..engine_model_type = "16k_zh-PY"
      ..appID = model.data?.appId ?? 0
      ..secretID = model.data?.secretId ?? ""
      ..secretKey = model.data?.secretKey ?? ""
      // 优化语音识别参数，解决识别不全问题
      ..vad_silence_time = 3000  // 语音断句检测阈值增加到3秒
      ..needvad = 0  // 关闭人声切分，避免句子被意外切断
      ..filter_dirty = 0  // 不过滤脏词
      ..filter_modal = 0  // 不过滤语气词
      ..filter_punc = 0  // 不过滤标点
      ..convert_num_mode = 1  // 开启数字智能转换
      ..word_info = 0;  // 不显示词级别时间戳，提高性能
    _config = config;
    return Future.value(_config);
  }
}
