import 'dart:convert';
import 'dart:typed_data';

import 'package:common_utils/common_utils.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/loading_status.dart';
import 'package:flutter_app_kouyu/common/http/models/analyze_model/analyze_model.dart';
import 'package:flutter_app_kouyu/common/http/models/audio_upload_model/audio_upload_model.dart';
import 'package:flutter_app_kouyu/common/http/models/soe_model/soe_model.dart';
import 'package:flutter_app_kouyu/common/provider/vlc_model.dart';
import 'package:flutter_app_kouyu/common/provider/voice_model.dart';
import 'package:flutter_app_kouyu/common/utils/common_util.dart';
import 'package:flutter_app_kouyu/common/utils/log.dart';
import 'package:flutter_app_kouyu/modules/chat/view/free_talk_page.dart';
import 'package:flutter_app_kouyu/repository/free_talk_repository.dart';
import 'package:flutter_vlc_player/src/vlc_player_controller.dart';

enum VoiceRecordStateEnum { idle, recording, finish }

class TalkVoiceProvider extends VoiceModel {
  bool _isDisposed = false;
  bool connecting = true;
  bool get isDisposed => _isDisposed;
  final FreeTalkRepository freeTalkRepository = FreeTalkRepository();
  CancelToken? cancelToken;
  TalkModel? requestingModel;
  SuccessCallback? successCallback;

  final TalkArgument argument;
  final BuildContext context;
  final num conversationId;
  VoiceRecordStateEnum recordStatus = VoiceRecordStateEnum.idle;
  //回复状态
  LoadingStatus replyStatus = LoadingStatus.idle;
  List<TalkModel> chatList = [];
  //是否静音
  bool voiceIsOpen = true;
  //字幕是否打开
  bool barrageIsOpen = false;
  //是否正播放回答
  bool isPlaying = false;

  TalkVoiceProvider(
    this.context,
    this.conversationId, {
    required this.argument,
  }) {
    Future.delayed(const Duration(milliseconds: 1000), () {
      notifyListeners();
      CommonUtils.playLocalVideo(
        "audio/call_connected.mp3",
        finish: () {
          connecting = false;
          notifyListeners();
          //开始录音
          startRecording();
        },
      );
    });
  }
  @override
  dispose() {
    _isDisposed = true;
    cancelRequest();
    successCallback = null;
    // 停止录音
    if (recordStatus == VoiceRecordStateEnum.recording) {
      stopRecording();
    }
    // 清理播放回调
    if (CommonUtils.finishBlock != null) {
      CommonUtils.finishBlock = null;
    }
    super.dispose();
  }

  void toggletVoiceOpen() {
    voiceIsOpen = !voiceIsOpen;
    notifyListeners();
    if (!voiceIsOpen) {
      //静音则停止录音
      stopRecording();
    } else {
      //开始录音
      startRecording();
    }
  }

  void toggleBarrageOpen() {
    barrageIsOpen = !barrageIsOpen;
    notifyListeners();
  }

  @override
  Future<bool> stopRecording() async {
    LogUtil.d("停止录音", tag: "app");
    recordStatus = VoiceRecordStateEnum.finish;
    notifyListeners();
    return await super.stopRecording();
  }

  void cancelRequest() {
    cancelToken?.cancel("用户取消请求");
    if (requestingModel != null) {
      chatList.remove(requestingModel);
    }
  }

  void startRecording() async {
    if (_isDisposed) {
      LogUtil.d("TalkVoiceProvider已被销毁，无法开始录音", tag: "app");
      return;
    }
    CommonUtils.finishBlock = null;
    await CommonUtils.stopPlay();
    isPlaying = false;
    if (!voiceIsOpen) {
      //静音则不录音
      recordStatus = VoiceRecordStateEnum.idle;
      notifyListeners();
      return;
    }
    if (recordStatus == VoiceRecordStateEnum.recording) {
      //如果正在录音则不重复开始
      LogUtil.d("已经开始录音，忽略", tag: "app");
      return;
    }
    recordStatus = VoiceRecordStateEnum.recording;
    LogUtil.d("开始录音", tag: "app");
    notifyListeners();
    successCallback ??= ({result, filepath}) async {
      if (_isDisposed) {
        LogUtil.d("TalkVoiceProvider已被销毁，忽略录音结果", tag: "app");
        return;
      }
      LogUtil.d("录音结束，自动发送" + result, tag: "app");
      recordStatus = VoiceRecordStateEnum.finish;
      notifyListeners();

      if (result.isEmpty) {
        // 延迟重新开始录音，避免无限循环
        Future.delayed(const Duration(milliseconds: 500), () {
          if (!_isDisposed && voiceIsOpen) {
            startRecording();
          }
        });
        return;
      }
      //添加问话
      TalkModel questionModel = TalkModel.questionModel(
          conversationId, result, "",
          soeStatus: const LoadingState(status: LoadingStatus.inProgress),
          analyzeStatus: const LoadingState(status: LoadingStatus.inProgress));
      sendChat(questionModel);
      //分析语音,教学模式不执行
      if (argument.model != 1) {
        analyze(questionModel);
      }
      try {
        Future.delayed(const Duration(seconds: 1), () async {
          if (_isDisposed) return;
          //延时1s，防止录音文件有问题
          AudioUploadModel value = await uploadVoice(filepath);
          //刷新url
          questionModel.audioUrl = value.data?.fileUrl;
          soe(questionModel);
        });
      } catch (_) {
        //刷新url
        questionModel.soeStatus =
            const LoadingState(status: LoadingStatus.failure);
        notifyListeners();
      }
    };

    try {
      await speak(
          isZh: argument.model == 1,
          silence_detect_duration: 2000,
          successCallback: successCallback);
    } catch (e) {
      LogUtil.d("录音异常: $e", tag: "app");
      recordStatus = VoiceRecordStateEnum.finish;
      notifyListeners();
      // 延迟重新开始录音，避免无限循环
      Future.delayed(const Duration(seconds: 1), () {
        if (!_isDisposed && voiceIsOpen) {
          startRecording();
        }
      });
    }
  }

  Future<void> sendChat(
    TalkModel question,
  ) async {
    LogUtil.d("发送对话", tag: "app");
    chatList.add(question);
    notifyListeners();
    replyStatus = LoadingStatus.inProgress;

    try {
      //请求答案
      cancelToken = CancelToken();
      final stream = await Api.chatStream({
        "question": question.question,
        "conversation_id": question.conversationId,
        "client_sentence_id": question.clientSentenceId,
        "topic": argument.topic,
        "mode": argument.model
      }, cancelToken: cancelToken);
      TalkModel model = TalkModel.replyModel(conversationId, null, null, null);
      requestingModel = model;
      chatList.add(model);
      try {
        await handleStreamResponse(stream, model);
        requestingModel = null;
        replyStatus = LoadingStatus.success;
        notifyListeners();
      } catch (e) {
        LogUtil.d("处理返回消息异常: ${e}", tag: "app");
        requestingModel = null;
        replyStatus = LoadingStatus.failure;
        notifyListeners();
        // 延迟重新开始录音
        Future.delayed(const Duration(seconds: 1), () {
          if (!_isDisposed && voiceIsOpen) {
            startRecording();
          }
        });
      }
    } catch (e) {
      LogUtil.d("发送失败", tag: "app");
      requestingModel = null;
      replyStatus = LoadingStatus.failure;
      notifyListeners();
      // 延迟重新开始录音
      Future.delayed(const Duration(seconds: 1), () {
        if (!_isDisposed && voiceIsOpen) {
          startRecording();
        }
      });
    }
  }

  Future<void> handleStreamResponse(Stream stream, TalkModel model) async {
    // 处理流式响应
    //停止之前的播放
    await CommonUtils.stopPlay();
    CommonUtils.finishBlock = null;
    model.question = "";
    await for (var data in stream) {
      final bytes = data as Uint8List;
      final decodedData = utf8.decode(bytes);
      final List<String> parts = decodedData.split("\n");
      for (final part in parts) {
        if (part.isEmpty) {
          continue;
        }
        LogUtil.d("流式数据: $part", tag: "app");
        TalkSteamModel talkSteamModel = TalkSteamModel.fromString(part);
        switch (talkSteamModel.type) {
          case TalkSteamType.text:
            model.question = (model.question ?? "") + talkSteamModel.content;
            notifyListeners();
            break;
          case TalkSteamType.audioUrl:
            try {
              isPlaying = true;
              notifyListeners();
              CommonUtils.addUrl(talkSteamModel.content);
            } catch (e) {
              LogUtil.d("播放异常: ${e}", tag: "app");
              isPlaying = false;
              notifyListeners();
            }

          case TalkSteamType.fullAudioUrl:
            model.audioUrl = talkSteamModel.content;
            //返回完整录音，监听播放结束后继续录音
            if (CommonUtils.urls.isEmpty && CommonUtils.url == null) {
              isPlaying = false;
              notifyListeners();
              if (!_isDisposed && voiceIsOpen) {
                startRecording();
              }
            } else {
              CommonUtils.finishBlock = () {
                //防止其他地方手动stop触发
                CommonUtils.finishBlock = null;
                isPlaying = false;
                notifyListeners();
                if (!_isDisposed && voiceIsOpen) {
                  startRecording();
                }
              };
            }

            break;
          case TalkSteamType.unknown:
            LogUtil.d("未知类型数据: ${talkSteamModel.content}", tag: "app");
            break;
        }
      }
    }
    LogUtil.d("流式数据结束", tag: "app");
  }

  Future<void> soe(TalkModel model) async {
    String sence = "scenario_actual_combat";
    if (argument.model == 1) {
      sence = "teaching_mode";
    }
    model.soeStatus = const LoadingState(status: LoadingStatus.inProgress);
    try {
      final SoeModel result = await freeTalkRepository.soe(model, sence);
      model.soeModel = result;
      model.soeStatus = const LoadingState(status: LoadingStatus.success);
      notifyListeners();
    } catch (e) {
      model.soeStatus = const LoadingState(status: LoadingStatus.failure);
      notifyListeners();
    }
  }

  Future<void> analyze(TalkModel model) async {
    if (_isDisposed) return;
    String sence = "scenario_actual_combat";
    if (argument.model == 1) {
      sence = "teaching_mode";
    }
    model.analyzeStatus = const LoadingState(status: LoadingStatus.inProgress);
    try {
      final AnalyzeModel result = await freeTalkRepository.analyze(model, sence);
      if (!_isDisposed) {
        model.analyzeStatus = const LoadingState(status: LoadingStatus.success);
        model.analyzeModel = result;
        notifyListeners();
      }
    } catch (e) {
      if (!_isDisposed) {
        model.analyzeStatus = const LoadingState(status: LoadingStatus.failure);
        notifyListeners();
      }
    }
  }

  Future<AudioUploadModel> uploadVoice(String filePath) async {
    return freeTalkRepository.upload(filePath,
        topicCode: argument.topic, sence: "scene_dialogue");
  }
}
